import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON>roy, OnChanges, SimpleChanges, inject, ChangeDetectorRef, ViewContainerRef, ComponentRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

import {
  MultiColumnDropdownService,
  ProcessedColumnData,
  SortedColumn,
  MultiColumnDropdownOption
} from '../../services/multi-column-dropdown.service';
// Import the existing dynamic query component
import { DynamicQueryComponent } from '../../../dynamic-query/dynamic-query.component';

// Simple interfaces for dynamic query integration
export interface DynamicQueryConfig {
  queryBuilderId: string;
  columns: any[];
  title?: string;
  description?: string;
}

export interface DynamicQueryEvent {
  type: 'OPEN_QUERY' | 'APPLY_FILTERS' | 'SELECT_ITEM' | 'CLOSE_QUERY';
  config?: DynamicQueryConfig;
  selectedItem?: any;
  filters?: any[];
}

export interface MultiColumnDropdownConfig {
  queryBuilderId: string;
  placeholder?: string;
  emptyMessage?: string;
  tooltip?: string;
  maxHeight?: string;
  limit?: number;
  searchDebounceTime?: number;
  showTableHeaders?: boolean;
  primaryDisplayField?: string;
}

export interface MultiColumnValueChangeEvent {
  fieldName: string;
  value: any;
  option: MultiColumnDropdownOption;
  displayText: string;
}

@Component({
  selector: 'app-multi-column-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './multi-column-dropdown.component.html',
  styleUrl: './multi-column-dropdown.component.scss'
})
export class MultiColumnDropdownComponent implements OnInit, OnDestroy, OnChanges {
  
  // Core inputs
  @Input() fieldName!: string;
  @Input() formControl!: FormControl;
  @Input() config!: MultiColumnDropdownConfig;
  @Input() isDisabled: boolean = false;
  @Input() isReadonly: boolean = false;
  @Input() cssClass: string = '';
  @Input() containerClass: string = '';
  @Input() inputId?: string;

  // Outputs
  @Output() valueChange = new EventEmitter<MultiColumnValueChangeEvent>();
  @Output() dynamicQueryEvent = new EventEmitter<DynamicQueryEvent>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() dropdownToggle = new EventEmitter<boolean>();
  @Output() optionSelect = new EventEmitter<MultiColumnDropdownOption>();

  // Internal state
  showDropdown: boolean = false;
  isLoading: boolean = false;
  searchTimeout: any;
  
  // Column and data management
  columns: SortedColumn[] = [];
  allColumns: SortedColumn[] = [];
  filteredOptions: MultiColumnDropdownOption[] = [];
  hasMultipleColumns: boolean = false;
  showViewListButton: boolean = false;
  lastDynamicQueryEvent: DynamicQueryEvent | null = null;
  private dynamicQueryRef: ComponentRef<DynamicQueryComponent> | null = null;
  
  // Track when setting dropdown values to prevent input conflicts
  private settingDropdownValue: boolean = false;
  
  // Unique identifier for this dropdown instance
  public uniqueId: string = '';
  
  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();
  
  // Search subject for debouncing
  private searchSubject = new Subject<string>();

  private multiColumnService = inject(MultiColumnDropdownService);
  private cdr = inject(ChangeDetectorRef);
  private viewContainer = inject(ViewContainerRef);

  ngOnInit() {
    // Generate unique identifier
    this.uniqueId = this.inputId || `${this.fieldName}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // Setup search debouncing
    this.setupSearchDebouncing();

    // Update disabled state
    this.updateFormControlDisabledState();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update form control disabled state when inputs change
    if (changes['isDisabled'] || changes['isReadonly']) {
      this.updateFormControlDisabledState();
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Clean up dynamic query popup
    this.closeDynamicQueryPopup();
  }

  private setupSearchDebouncing(): void {
    this.searchSubject
      .pipe(
        debounceTime(this.config.searchDebounceTime || 300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(searchTerm => {
        this.performSearch(searchTerm);
      });
  }

  private updateFormControlDisabledState(): void {
    if (this.formControl) {
      if (this.isDisabled || this.isReadonly) {
        if (this.formControl.enabled) {
          this.formControl.disable();
        }
      } else {
        if (this.formControl.disabled) {
          this.formControl.enable();
        }
      }
    }
  }

  toggleDropdown(): void {
    if (this.isDisabled || this.isReadonly) {
      return;
    }

    if (!this.showDropdown) {
      const currentValue = this.formControl?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllOptions();
      } else {
        this.searchSubject.next(currentValue);
      }
    } else {
      this.showDropdown = false;
    }

    this.dropdownToggle.emit(this.showDropdown);
  }

  onInputChange(event: Event): void {
    if (this.settingDropdownValue) {
      return;
    }

    const target = event.target as HTMLInputElement;
    const searchTerm = target.value;
    
    this.searchSubject.next(searchTerm);
    this.searchChange.emit(searchTerm);
  }

  onInputFocus(): void {
    if (!this.isDisabled && !this.isReadonly && !this.showDropdown) {
      this.toggleDropdown();
    }
  }

  onInputBlur(): void {
    setTimeout(() => {
      this.showDropdown = false;
      this.dropdownToggle.emit(false);
    }, 200);
  }

  selectOption(option: MultiColumnDropdownOption): void {
    this.setDropdownValue(option);
    this.optionSelect.emit(option);
  }

  private loadAllOptions(): void {
    // Check cache first
    const cachedData = this.multiColumnService.getCachedData(this.config.queryBuilderId);
    const cachedColumnDef = this.multiColumnService.getCachedColumnDef(this.config.queryBuilderId);
    
    if (cachedData && cachedColumnDef) {
      this.processColumnData({
        columns: this.multiColumnService['sortColumnsByOrder'](cachedColumnDef),
        options: cachedData,
        hasMultipleColumns: Object.keys(cachedColumnDef).length > 1
      });
      this.showDropdown = true;
      return;
    }

    // Load from API
    this.loadFromApi();
  }

  private performSearch(searchTerm: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // Check if we have cached data for client-side filtering
    const cachedData = this.multiColumnService.getCachedData(this.config.queryBuilderId);
    const cachedColumnDef = this.multiColumnService.getCachedColumnDef(this.config.queryBuilderId);
    
    if (cachedData && cachedColumnDef) {
      // Use client-side filtering
      const filtered = this.multiColumnService.filterOptionsClientSide(cachedData, searchTerm);
      this.filteredOptions = filtered;
      this.showDropdown = true;
      this.cdr.detectChanges();
    } else {
      // Load from API with search term
      this.loadFromApi(searchTerm);
    }
  }

  private loadFromApi(searchTerm?: string): void {
    this.isLoading = true;
    
    this.multiColumnService.loadDropdownData(
      this.config.queryBuilderId,
      searchTerm,
      this.config.limit || 20
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (data: ProcessedColumnData) => {
        this.isLoading = false;
        this.processColumnData(data);
        this.showDropdown = true;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.isLoading = false;
        this.setEmptyDropdownState();
        console.error('Error loading dropdown data:', error);
      }
    });
  }

  private processColumnData(data: ProcessedColumnData): void {
    this.allColumns = data.columns; // Store all columns from API
    this.filteredOptions = data.options;
    this.hasMultipleColumns = data.hasMultipleColumns;

    // Always show only first 6 columns in dropdown table
    this.columns = this.allColumns.slice(0, 6);

    // Show "View List" button if there are more than 6 columns
    this.showViewListButton = this.allColumns.length > 6;

    // Debug information
    console.log('Processed Column Data:', {
      totalColumns: this.allColumns.length,
      displayedColumns: this.columns.length,
      showViewListButton: this.showViewListButton,
      hasMultipleColumns: this.hasMultipleColumns,
      allColumnsFromAPI: this.allColumns.map(col => col.fieldName),
      displayedColumnsInTable: this.columns.map(col => col.fieldName)
    });

    this.cdr.detectChanges();
  }



  private setEmptyDropdownState(): void {
    this.filteredOptions = [];
    this.showDropdown = true;
  }

  private setDropdownValue(option: MultiColumnDropdownOption): void {
    this.settingDropdownValue = true;

    if (this.formControl) {
      // Get the value to store (usually ID)
      const storedValue = this.multiColumnService.getOptionValue(option);
      this.formControl.setValue(storedValue);

      // Get display text for input field
      const displayText = this.multiColumnService.getOptionDisplayText(option, this.columns);

      // Set input display value
      setTimeout(() => {
        const inputElement = document.getElementById(this.uniqueId) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = displayText;
        }
      }, 0);

      // Update form control state
      this.formControl.markAsDirty();
      this.formControl.markAsTouched();
      this.formControl.updateValueAndValidity();
      this.cdr.detectChanges();
    }

    // Close dropdown
    this.showDropdown = false;

    // Emit value change event
    const displayText = this.multiColumnService.getOptionDisplayText(option, this.columns);
    const storedValue = this.multiColumnService.getOptionValue(option);
    
    this.valueChange.emit({
      fieldName: this.uniqueId,
      value: storedValue,
      option: option,
      displayText: displayText
    });

    // Clear the setting flag
    setTimeout(() => {
      this.settingDropdownValue = false;
    }, 100);
  }

  // Template helper methods
  get inputClass(): string {
    let classes = 'form-input';
    if (this.cssClass) {
      if (this.cssClass.includes('form-input')) {
        classes = this.cssClass;
      } else {
        classes += ` ${this.cssClass}`;
      }
    }
    if (this.isDisabled || this.isReadonly) {
      classes += ' disabled';
    }
    return classes;
  }

  get dropdownArrowIcon(): string {
    return this.showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  get emptyMessage(): string {
    return this.config.emptyMessage || 'No options found';
  }

  get placeholderText(): string {
    return this.config.placeholder || '';
  }

  get tooltipText(): string {
    return this.config.tooltip || 'Show options';
  }

  get dropdownMaxHeight(): string {
    return this.config.maxHeight || '300px';
  }

  get showHeaders(): boolean {
    return this.config.showTableHeaders !== false && this.hasMultipleColumns;
  }

  // Track by functions for performance
  trackByColumn(index: number, column: SortedColumn): string {
    return column.fieldName;
  }

  trackByOption(index: number, option: MultiColumnDropdownOption): string {
    return this.multiColumnService.getOptionValue(option) || index.toString();
  }

  // Template helper method
  getOptionDisplayText(option: MultiColumnDropdownOption): string {
    return this.multiColumnService.getOptionDisplayText(option, this.columns);
  }

  // Open dynamic query popup with all columns
  openDynamicQuery(): void {
    console.log('Opening Dynamic Query with all columns:', this.allColumns.length);

    // Create dynamic query config using all columns
    const queryConfig: DynamicQueryConfig = {
      queryBuilderId: this.config.queryBuilderId,
      columns: this.allColumns.map(col => ({
        fieldName: col.fieldName,
        label: col.label,
        dataType: this.inferDataType(col.fieldName),
        searchable: true,
        filterable: true,
        sortable: true
      })),
      title: `Advanced Search - ${this.config.placeholder || 'Select Item'}`,
      description: `Search and filter from ${this.allColumns.length} available columns`
    };

    // Create the event
    const event: DynamicQueryEvent = {
      type: 'OPEN_QUERY',
      config: queryConfig
    };

    // Track the event
    this.lastDynamicQueryEvent = event;

    // Emit dynamic query event for parent components to handle
    this.dynamicQueryEvent.emit(event);

    console.log('Dynamic Query Config:', queryConfig);
    console.log('Event emitted:', event);

    // Create and show the dynamic query component
    this.createDynamicQueryPopup(queryConfig);

    // Close current dropdown
    this.showDropdown = false;
  }

  // Create the dynamic query component
  private createDynamicQueryPopup(config: DynamicQueryConfig): void {
    // Close any existing query
    this.closeDynamicQueryPopup();

    // Create a non-blocking backdrop (just for visual effect)
    const backdrop = document.createElement('div');
    backdrop.className = 'dynamic-query-backdrop';
    backdrop.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(1px);
      z-index: 1999;
      pointer-events: none;
      overflow: visible;
    `;

    // Create draggable container for the dynamic query
    const container = document.createElement('div');
    container.className = 'draggable-query-container';

    // Always calculate center position dynamically
    const containerWidth = Math.min(1200, window.innerWidth * 0.95);
    const containerMaxHeight = Math.min(window.innerHeight * 0.9, 800);

    // Center horizontally and vertically
    const centerX = Math.max(20, (window.innerWidth - containerWidth) / 2);
    const centerY = Math.max(20, (window.innerHeight - containerMaxHeight) / 2);

    container.style.cssText = `
      background: white;
      border-radius: 12px;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      max-width: 95vw;
      max-height: 90vh;
      width: ${containerWidth}px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      position: fixed;
      top: ${centerY}px;
      left: ${centerX}px;
      z-index: 2001;
      cursor: default;
      border: 2px solid #3b82f6;
    `;

    console.log('Popup centered at:', { centerX, centerY, containerWidth, containerMaxHeight });

    // Add draggable header
    const header = document.createElement('div');
    header.className = 'drag-handle';
    header.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      color: white;
      cursor: move;
      user-select: none;
      border-radius: 12px 12px 0 0;
    `;
    header.innerHTML = `
      <div style="display: flex; align-items: center; gap: 12px;">
        <span style="font-size: 18px; opacity: 0.8;">⋮⋮</span>
        <div>
          <h2 style="margin: 0; font-size: 18px; font-weight: 600;">${config.title}</h2>
          <p style="margin: 2px 0 0 0; opacity: 0.9; font-size: 12px;">${config.description}</p>
        </div>
      </div>
      <div style="display: flex; align-items: center; gap: 8px;">
        <button id="center-query" style="background: none; border: none; color: white; cursor: pointer; padding: 4px 8px; border-radius: 4px; opacity: 0.8;" title="Center Window">
          <span style="font-size: 16px;">⌖</span>
        </button>
        <button id="minimize-query" style="background: none; border: none; color: white; cursor: pointer; padding: 4px 8px; border-radius: 4px; opacity: 0.8;" title="Minimize">
          <span style="font-size: 16px;">−</span>
        </button>
        <button id="close-query" style="background: none; border: none; color: white; cursor: pointer; padding: 4px 8px; border-radius: 4px; opacity: 0.8;" title="Close">
          <span style="font-size: 18px;">×</span>
        </button>
      </div>
    `;

    // Create the dynamic query component
    this.dynamicQueryRef = this.viewContainer.createComponent(DynamicQueryComponent);

    // Configure the component with the queryBuilderId
    this.dynamicQueryRef.instance.queryName = config.queryBuilderId;

    // Pass column definition for proper table display
    const cachedColumnDef = this.multiColumnService.getCachedColumnDef(config.queryBuilderId);
    (this.dynamicQueryRef.instance as any).columnDefinition = cachedColumnDef || null;
    console.log('Passed columnDef to dynamic query:', cachedColumnDef);

    // Override the onRowClick method to handle row selection
    const originalOnRowClick = this.dynamicQueryRef.instance.onRowClick.bind(this.dynamicQueryRef.instance);
    this.dynamicQueryRef.instance.onRowClick = (row: any) => {
      console.log('Row clicked in dynamic query:', row);

      // Call original method first
      originalOnRowClick(row);

      // Handle row selection for dropdown
      this.handleRowSelection(row);
    };

    // Style the component container
    const queryElement = this.dynamicQueryRef.location.nativeElement;
    queryElement.style.cssText = `
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 20px;
      position: relative;
      z-index: 2002;
      min-height: 0;
      cursor: default;
    `;

    // Assemble the modal
    container.appendChild(header);
    container.appendChild(queryElement);
    document.body.appendChild(backdrop);
    document.body.appendChild(container);

    // Add close, minimize, and center functionality
    const closeBtn = header.querySelector('#close-query');
    const minimizeBtn = header.querySelector('#minimize-query');
    const centerBtn = header.querySelector('#center-query');

    closeBtn?.addEventListener('click', () => this.closeDynamicQueryPopup());
    minimizeBtn?.addEventListener('click', () => this.toggleMinimizeQuery(container, queryElement));
    centerBtn?.addEventListener('click', () => this.recenterPopup(container));

    // Add drag functionality
    this.makeDraggable(container, header);

    // Add window resize handler to keep popup centered
    const resizeHandler = () => this.recenterPopup(container);
    window.addEventListener('resize', resizeHandler);

    // Store references for cleanup
    (this.dynamicQueryRef as any).backdrop = backdrop;
    (this.dynamicQueryRef as any).container = container;
    (this.dynamicQueryRef as any).resizeHandler = resizeHandler;

    // Add global CSS to ensure dropdowns work inside modal
    this.addModalDropdownStyles();

    console.log('Dynamic Query Component created and displayed');
  }

  // Add styles to ensure dropdowns work inside modal
  private addModalDropdownStyles(): void {
    const styleId = 'dynamic-query-modal-styles';

    // Remove existing styles if any
    const existingStyle = document.getElementById(styleId);
    if (existingStyle) {
      existingStyle.remove();
    }

    // Create new style element
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      /* Ensure Angular Material dropdowns work inside modal */
      .cdk-overlay-container {
        z-index: 2100 !important;
      }

      .cdk-overlay-pane {
        z-index: 2101 !important;
      }

      /* Ensure mat-select dropdowns work */
      .mat-mdc-select-panel {
        z-index: 2102 !important;
      }

      /* Ensure other dropdowns work */
      .mat-autocomplete-panel {
        z-index: 2102 !important;
      }

      /* Dynamic query specific overrides */
      .dynamic-query-backdrop .mat-mdc-form-field {
        overflow: visible !important;
      }

      .dynamic-query-backdrop .mat-mdc-select {
        overflow: visible !important;
      }

      /* Filter group overrides */
      .dynamic-query-backdrop app-filter-group {
        overflow: visible !important;
      }

      .dynamic-query-backdrop app-filter-group .mat-mdc-form-field {
        overflow: visible !important;
      }

      /* Results table container */
      .dynamic-query-backdrop .results-container {
        max-height: 400px !important;
        overflow-y: auto !important;
        overflow-x: auto !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 4px !important;
        margin-top: 16px !important;
      }

      /* Results table */
      .dynamic-query-backdrop .results-table {
        width: 100% !important;
        min-width: max-content !important;
      }

      /* Table wrapper for horizontal scroll */
      .dynamic-query-backdrop .table-wrapper {
        overflow-x: auto !important;
        max-height: 400px !important;
        overflow-y: auto !important;
      }

      /* Mat table overrides */
      .dynamic-query-backdrop .mat-mdc-table {
        max-height: 400px !important;
        overflow: auto !important;
      }

      .dynamic-query-backdrop .mat-mdc-table-container {
        max-height: 400px !important;
        overflow: auto !important;
      }

      /* Query builder container */
      .dynamic-query-backdrop .query-builder-container {
        max-height: none !important;
        overflow: visible !important;
      }

      .dynamic-query-backdrop .query-builder-container .mat-mdc-card-content {
        overflow: visible !important;
      }

      /* Specific targeting for dynamic query results */
      .dynamic-query-backdrop .results-container {
        max-height: 50vh !important;
        overflow: hidden !important;
        margin-top: 20px !important;
      }

      .dynamic-query-backdrop .results-container .mat-mdc-card {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
      }

      .dynamic-query-backdrop .results-container .mat-mdc-card-content {
        flex: 1 !important;
        overflow: hidden !important;
        padding: 16px !important;
      }

      .dynamic-query-backdrop .table-wrapper {
        max-height: 40vh !important;
        overflow: auto !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 4px !important;
      }

      .dynamic-query-backdrop .results-table {
        width: 100% !important;
      }

      .dynamic-query-backdrop .results-table th,
      .dynamic-query-backdrop .results-table td {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 200px !important;
      }

      /* Results header */
      .dynamic-query-backdrop .results-header {
        flex-shrink: 0 !important;
        padding: 16px !important;
      }

      /* Download button positioning */
      .dynamic-query-backdrop .download-button {
        margin-left: auto !important;
      }

      /* Draggable container styles */
      .draggable-query-container {
        transition: box-shadow 0.2s ease !important;
      }

      .draggable-query-container:hover {
        box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.35) !important;
      }

      .drag-handle {
        transition: background 0.2s ease !important;
      }

      .drag-handle:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
      }

      .drag-handle button:hover {
        background: rgba(255, 255, 255, 0.2) !important;
      }

      /* Prevent text selection during drag */
      .draggable-query-container.dragging {
        user-select: none !important;
        box-shadow: 0 35px 70px -12px rgba(0, 0, 0, 0.4) !important;
        transform: scale(1.02) !important;
        transition: none !important;
      }

      .draggable-query-container.dragging * {
        user-select: none !important;
        pointer-events: none !important;
      }

      .draggable-query-container.dragging .drag-handle {
        pointer-events: auto !important;
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
      }

      .draggable-query-container.dragging .drag-handle button {
        pointer-events: auto !important;
      }

      /* Smooth transitions when not dragging */
      .draggable-query-container:not(.dragging) {
        transition: all 0.2s ease !important;
      }

      /* Better cursor indicators */
      .drag-handle {
        cursor: grab !important;
      }

      .drag-handle:active {
        cursor: grabbing !important;
      }

      /* Simple table row hover - just hand cursor */
      .draggable-query-container .mat-mdc-row {
        cursor: pointer !important;
      }

      .draggable-query-container .mat-mdc-row:hover {
        cursor: pointer !important;
      }

      .draggable-query-container .results-table tr {
        cursor: pointer !important;
      }

      .draggable-query-container .results-table tr:hover {
        cursor: pointer !important;
      }
    `;

    document.head.appendChild(style);
    console.log('Modal dropdown styles added');
  }

  // Handle row selection from dynamic query results
  private handleRowSelection(row: any): void {
    console.log('Handling row selection from dynamic query:', row);

    // Set the selected value in the form control
    this.selectOption(row);

    // Close the popup
    this.closeDynamicQueryPopup();

    // Emit the selection event
    this.valueChange.emit({
      fieldName: this.fieldName,
      value: row,
      option: row,
      displayText: this.multiColumnService.getOptionDisplayText(row, this.columns)
    });
  }

  // Make element draggable with improved logic
  private makeDraggable(element: HTMLElement, handle: HTMLElement): void {
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let elementX = 0;
    let elementY = 0;

    // Get initial position
    const rect = element.getBoundingClientRect();
    elementX = rect.left;
    elementY = rect.top;

    const dragStart = (e: MouseEvent) => {
      // Only start drag if clicking on the header (not buttons)
      const target = e.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        return;
      }

      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;

      // Get current position
      const currentRect = element.getBoundingClientRect();
      elementX = currentRect.left;
      elementY = currentRect.top;

      // Visual feedback - only change header cursor
      handle.style.cursor = 'grabbing';
      element.classList.add('dragging');

      // Prevent text selection
      e.preventDefault();
      document.body.style.userSelect = 'none';
    };

    const dragEnd = () => {
      if (!isDragging) return;

      isDragging = false;
      handle.style.cursor = 'grab';
      element.classList.remove('dragging');
      document.body.style.userSelect = '';
    };

    const drag = (e: MouseEvent) => {
      if (!isDragging) return;

      e.preventDefault();

      // Calculate new position
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      let newX = elementX + deltaX;
      let newY = elementY + deltaY;

      // Constrain to viewport with some padding
      const padding = 20;
      const maxX = window.innerWidth - element.offsetWidth - padding;
      const maxY = window.innerHeight - element.offsetHeight - padding;

      newX = Math.max(padding, Math.min(newX, maxX));
      newY = Math.max(padding, Math.min(newY, maxY));

      // Apply position using left/top instead of transform
      element.style.left = `${newX}px`;
      element.style.top = `${newY}px`;
      element.style.transform = 'none';
    };

    // Touch support for mobile
    const touchStart = (e: TouchEvent) => {
      if (e.touches.length === 1) {
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousedown', {
          clientX: touch.clientX,
          clientY: touch.clientY
        });
        dragStart(mouseEvent);
      }
    };

    const touchMove = (e: TouchEvent) => {
      if (e.touches.length === 1 && isDragging) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousemove', {
          clientX: touch.clientX,
          clientY: touch.clientY
        });
        drag(mouseEvent);
      }
    };

    const touchEnd = () => {
      dragEnd();
    };

    // Add event listeners for mouse and touch
    handle.addEventListener('mousedown', dragStart);
    handle.addEventListener('touchstart', touchStart, { passive: false });
    document.addEventListener('mousemove', drag);
    document.addEventListener('touchmove', touchMove, { passive: false });
    document.addEventListener('mouseup', dragEnd);
    document.addEventListener('touchend', touchEnd);

    // Prevent drag on buttons
    const buttons = handle.querySelectorAll('button');
    buttons.forEach(button => {
      button.addEventListener('mousedown', (e) => {
        e.stopPropagation();
      });
    });

    // Store cleanup functions
    (element as any).dragCleanup = () => {
      handle.removeEventListener('mousedown', dragStart);
      handle.removeEventListener('touchstart', touchStart);
      document.removeEventListener('mousemove', drag);
      document.removeEventListener('touchmove', touchMove);
      document.removeEventListener('mouseup', dragEnd);
      document.removeEventListener('touchend', touchEnd);
      buttons.forEach(button => {
        button.removeEventListener('mousedown', (e) => e.stopPropagation());
      });
      // Reset body styles
      document.body.style.userSelect = '';
    };
  }

  // Toggle minimize/maximize query popup
  private toggleMinimizeQuery(container: HTMLElement, queryElement: HTMLElement): void {
    const isMinimized = container.style.height === '60px';

    if (isMinimized) {
      // Restore
      container.style.height = 'auto';
      container.style.maxHeight = '90vh';
      queryElement.style.display = 'block';
      container.style.width = '1200px';
      const minimizeBtn = container.querySelector('#minimize-query span');
      if (minimizeBtn) minimizeBtn.textContent = '−';
    } else {
      // Minimize
      container.style.height = '60px';
      container.style.maxHeight = '60px';
      queryElement.style.display = 'none';
      container.style.width = '400px';
      const minimizeBtn = container.querySelector('#minimize-query span');
      if (minimizeBtn) minimizeBtn.textContent = '□';
    }
  }

  // Recenter popup when window is resized
  private recenterPopup(container: HTMLElement): void {
    if (!container) return;

    // Get current container dimensions
    const rect = container.getBoundingClientRect();
    const containerWidth = rect.width;
    const containerHeight = rect.height;

    // Calculate new center position
    const centerX = Math.max(20, (window.innerWidth - containerWidth) / 2);
    const centerY = Math.max(20, (window.innerHeight - containerHeight) / 2);

    // Apply new position
    container.style.left = `${centerX}px`;
    container.style.top = `${centerY}px`;
    container.style.transform = 'none';

    console.log('Popup recentered to:', { centerX, centerY, containerWidth, containerHeight });
  }

  // Close the dynamic query popup
  private closeDynamicQueryPopup(): void {
    if (this.dynamicQueryRef) {
      // Remove backdrop if it exists
      const backdrop = (this.dynamicQueryRef as any).backdrop;
      if (backdrop && backdrop.parentNode) {
        backdrop.parentNode.removeChild(backdrop);
      }

      // Remove container if it exists
      const container = (this.dynamicQueryRef as any).container || document.querySelector('.draggable-query-container');
      if (container) {
        // Clean up drag listeners
        if ((container as any).dragCleanup) {
          (container as any).dragCleanup();
        }
        container.remove();
      }

      // Remove resize handler
      const resizeHandler = (this.dynamicQueryRef as any).resizeHandler;
      if (resizeHandler) {
        window.removeEventListener('resize', resizeHandler);
      }

      // Remove modal styles
      const styleElement = document.getElementById('dynamic-query-modal-styles');
      if (styleElement) {
        styleElement.remove();
      }

      // Destroy the component
      this.dynamicQueryRef.destroy();
      this.dynamicQueryRef = null;
      console.log('Dynamic Query Component closed');
    }
  }

  // Infer data type from field name
  private inferDataType(fieldName: string): string {
    const lowerFieldName = fieldName.toLowerCase();

    if (lowerFieldName.includes('date') || lowerFieldName.includes('time')) {
      return 'date';
    }
    if (lowerFieldName.includes('amount') || lowerFieldName.includes('price') ||
        lowerFieldName.includes('salary') || lowerFieldName.includes('income') ||
        lowerFieldName.includes('count') || lowerFieldName.includes('number') ||
        lowerFieldName === 'id' || lowerFieldName.includes('code')) {
      return 'number';
    }
    if (lowerFieldName.includes('is') || lowerFieldName.includes('has') ||
        lowerFieldName.includes('enabled') || lowerFieldName.includes('active')) {
      return 'boolean';
    }

    return 'string';
  }
}
